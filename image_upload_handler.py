import os
import json
import time
import requests
import gspread
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
from oauth2client.service_account import ServiceAccountCredentials
from webdriver_manager.chrome import ChromeDriverManager
import sys
import random
import math
import platform
from googleapiclient.discovery import build
import traceback

sys.stdout.reconfigure(encoding='utf-8')

# ───── SETUP ─────────────────────────────────────────────────────────────────────

def get_driver():
    chrome_options = webdriver.ChromeOptions()
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
    chrome_options.add_experimental_option("useAutomationExtension", False)
    
    try:
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
    except Exception as e:
        print(f"[WARNING] ChromeDriverManager failed: {e}")
        try:
            driver = webdriver.Chrome(options=chrome_options)
        except Exception as e2:
            print(f"[ERROR] Both methods failed: {e2}")
            raise
    
    return driver

def get_google_sheet_data():
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    creds_path = r"./credentials.json"
    creds = ServiceAccountCredentials.from_json_keyfile_name(creds_path, scope)
    client = gspread.authorize(creds)
    sheet = client.open_by_key("1F0QwNeAi35W9ZCRgywswGRrM11_HumETGak6zRHXizU").worksheet("Sheet1")
    return sheet.get_all_records()

def get_drive_service():
    """Tạo service để truy cập Google Drive API"""
    scope = ["https://www.googleapis.com/auth/drive.readonly"]
    creds_path = r"./credentials.json"
    creds = ServiceAccountCredentials.from_json_keyfile_name(creds_path, scope)
    service = build('drive', 'v3', credentials=creds)
    return service

def get_folder_id_from_url(folder_url):
    """Lấy folder ID từ URL Google Drive folder"""
    match = re.search(r"folders/([a-zA-Z0-9_-]+)", folder_url)
    if match:
        return match.group(1)
    return None

def get_images_from_folder(folder_url):
    """Lấy danh sách tất cả file ảnh từ Google Drive folder"""
    folder_id = get_folder_id_from_url(folder_url)
    if not folder_id:
        return []
    
    try:
        service = get_drive_service()
        query = f"'{folder_id}' in parents and trashed=false"
        
        results = service.files().list(
            q=query,
            fields="files(id, name, mimeType)",
            pageSize=1000
        ).execute()
        
        files = results.get('files', [])
        image_urls = []
        
        for file in files:
            if file['mimeType'].startswith('image/'):
                file_url = f"https://drive.google.com/file/d/{file['id']}/view"
                image_urls.append(file_url)
        
        return image_urls
        
    except Exception as e:
        print(f"[ERROR] Lỗi khi truy cập folder: {e}")
        return []

def get_drive_direct_link(url):
    match = re.search(r"https://drive\.google\.com/file/d/([a-zA-Z0-9_-]+)", url)
    if match:
        file_id = match.group(1)
        return f"https://drive.google.com/uc?export=download&id={file_id}"
    return url

def process_image_urls(image_input):
    """Xử lý input có thể là folder URL hoặc danh sách URL ảnh"""
    image_input = image_input.strip()
    
    if "drive.google.com/drive/folders/" in image_input:
        return get_images_from_folder(image_input)
    
    urls = [url.strip() for url in image_input.split(",") if url.strip()]
    return urls

def get_local_images(image_folder="image"):
    """Lấy tất cả ảnh từ thư mục local"""
    # Sử dụng đường dẫn tuyệt đối
    image_folder = r"C:\Users\<USER>\Documents\Zalo Received Files\gift\gift\image"
    
    if not os.path.exists(image_folder):
        print(f"[WARNING] Thư mục {image_folder} không tồn tại")
        # Tạo thư mục nếu chưa có
        try:
            os.makedirs(image_folder, exist_ok=True)
            print(f"[INFO] Đã tạo thư mục {image_folder}")
        except Exception as e:
            print(f"[ERROR] Không thể tạo thư mục: {e}")
        return []
    
    image_paths = []
    # Hỗ trợ nhiều định dạng ảnh và video
    supported_formats = ('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif', '.svg', '.mp4', '.avi', '.mov', '.wmv', '.flv', '.mkv')
    
    try:
        all_files = os.listdir(image_folder)
        print(f"[INFO] Tìm thấy {len(all_files)} file trong thư mục")
        
        for file in all_files:
            if file.lower().endswith(supported_formats):
                image_path = os.path.join(image_folder, file)
                # Kiểm tra file không rỗng và có thể đọc được
                if os.path.isfile(image_path) and os.path.getsize(image_path) > 0:
                    try:
                        # Kiểm tra quyền đọc file
                        with open(image_path, 'rb') as f:
                            f.read(1)  # Đọc 1 byte để test
                        image_paths.append(os.path.abspath(image_path))
                        print(f"[INFO] Thêm file: {file} ({os.path.getsize(image_path)} bytes)")
                    except Exception as e:
                        print(f"[WARNING] Không thể đọc file {file}: {e}")
                else:
                    print(f"[WARNING] File {file} rỗng hoặc không hợp lệ")
        
        # Sắp xếp theo tên file để đảm bảo thứ tự nhất quán
        image_paths.sort()
        print(f"[INFO] Tổng cộng tìm thấy {len(image_paths)} file ảnh/video hợp lệ trong thư mục local")
        
        # Hiển thị danh sách file
        if image_paths:
            print("[INFO] Danh sách file sẽ upload:")
            for i, path in enumerate(image_paths, 1):
                size_mb = os.path.getsize(path) / (1024 * 1024)
                print(f"  {i}. {os.path.basename(path)} ({size_mb:.2f} MB)")
        
        return image_paths
        
    except Exception as e:
        print(f"[ERROR] Lỗi khi đọc thư mục ảnh: {e}")
        traceback.print_exc()
        return []

def delete_uploaded_images(image_paths):
    """Xóa các file ảnh sau khi upload thành công"""
    if not image_paths:
        print("[INFO] Không có ảnh nào để xóa")
        return
        
    print(f"[INFO] Bắt đầu xóa {len(image_paths)} file sau khi upload thành công...")
    deleted_count = 0
    failed_count = 0
    
    for image_path in image_paths:
        try:
            if os.path.exists(image_path):
                # Kiểm tra file có đang được sử dụng không
                try:
                    # Thử mở file để kiểm tra
                    with open(image_path, 'r+b') as f:
                        pass
                except IOError:
                    print(f"[WARNING] File đang được sử dụng, thử lại sau: {os.path.basename(image_path)}")
                    time.sleep(2)
                
                # Xóa file
                os.remove(image_path)
                deleted_count += 1
                print(f"[SUCCESS] Đã xóa: {os.path.basename(image_path)}")
            else:
                print(f"[WARNING] File không tồn tại: {os.path.basename(image_path)}")
        except PermissionError:
            print(f"[ERROR] Không có quyền xóa file: {os.path.basename(image_path)}")
            failed_count += 1
        except Exception as e:
            print(f"[ERROR] Không thể xóa {os.path.basename(image_path)}: {e}")
            failed_count += 1
    
    print(f"[INFO] Kết quả xóa file: {deleted_count} thành công, {failed_count} thất bại, tổng {len(image_paths)} file")
    
    # Kiểm tra thư mục sau khi xóa
    try:
        remaining_files = get_local_images()
        if remaining_files:
            print(f"[INFO] Còn lại {len(remaining_files)} file trong thư mục")
        else:
            print("[INFO] Thư mục đã sạch")
    except:
        pass

# ───── LOGIN ─────────────────────────────────────────────────────────────────────

def login_with_cookies(driver, group_url, cookie_file):
    try:
        with open(cookie_file, "r", encoding="utf-8") as f:
            cookies = json.load(f)

        driver.get("https://www.facebook.com")
        WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))

        for cookie in cookies:
            cookie.pop("storeId", None)
            cookie.pop("id", None)
            if "sameSite" in cookie and cookie["sameSite"].lower() in ["no_restriction", "unspecified"]:
                cookie["sameSite"] = "None"
            try:
                driver.add_cookie(cookie)
            except Exception as e:
                print(f"[WARNING] Cookie error: {cookie.get('name')}")

        driver.get(group_url)
        WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.TAG_NAME, "body")))

        if "login" in driver.current_url:
            print("[ERROR] Cookie sai hoặc hết hạn!")
            return False

        print("[INFO] Đăng nhập thành công")
        return True

    except Exception as e:
        print(f"[ERROR] Đăng nhập thất bại: {e}")
        return False

# ───── UPLOAD ẢNH ĐƠN GIẢN ───────────────────────────────────────────────────────

def upload_images_simple(driver, image_paths):
    """Upload tất cả ảnh cùng lúc - phương pháp tối ưu"""
    try:
        print(f"[INFO] Bắt đầu upload {len(image_paths)} ảnh...")
        print(f"[INFO] Danh sách ảnh: {[os.path.basename(p) for p in image_paths[:10]]}...")  # Hiển thị 10 ảnh đầu
        
        # Kiểm tra tất cả file có tồn tại không
        valid_paths = []
        for path in image_paths:
            if os.path.exists(path) and os.path.getsize(path) > 0:
                valid_paths.append(path)
            else:
                print(f"[WARNING] File không hợp lệ: {path}")
        
        if not valid_paths:
            print("[ERROR] Không có ảnh hợp lệ để upload")
            return False
            
        print(f"[INFO] Có {len(valid_paths)} ảnh hợp lệ để upload")
        
        # Click add photo/video button
        add_btn = WebDriverWait(driver, 15).until(
            EC.element_to_be_clickable((
                By.XPATH,
                "//div[@aria-label='Ảnh/video' or @aria-label='Photo/video' or contains(@aria-label, 'Photo') or contains(@aria-label, 'video')]"
            ))
        )
        driver.execute_script("arguments[0].click();", add_btn)
        time.sleep(3)

        # Find file input
        file_input = WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.XPATH, "//input[@type='file']"))
        )
        
        # Kiểm tra và đặt thuộc tính multiple
        has_multiple = driver.execute_script("return arguments[0].hasAttribute('multiple');", file_input)
        if not has_multiple:
            driver.execute_script("arguments[0].setAttribute('multiple', 'multiple');", file_input)
            print("[INFO] Đã đặt thuộc tính multiple cho file input")
        
        # Method 1: Upload tất cả file paths cùng lúc
        print(f"[INFO] Thử phương pháp 1: Upload {len(valid_paths)} ảnh cùng lúc...")
        
        # Thử với cách khác nhau để gửi multiple files
        try:
            # Cách 1: Dùng JavaScript để set files
            file_list_js = []
            for i, path in enumerate(valid_paths):
                file_list_js.append(f"files[{i}] = new File([], '{os.path.basename(path)}');")
            
            # Cách 2: Gửi tất cả paths với ký tự phân cách khác nhau
            path_separators = ["\n", ";", "|", ","]
            upload_success = False
            
            for separator in path_separators:
                try:
                    all_paths = separator.join(valid_paths)
                    print(f"[INFO] Thử với separator '{separator}'...")
                    file_input.send_keys(all_paths)
                    time.sleep(3)
                    
                    # Kiểm tra xem có ảnh nào được load không
                    try:
                        WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, "//img[contains(@src, 'blob:') or contains(@src, 'data:image')]"))
                        )
                        uploaded_images = driver.find_elements(By.XPATH, "//img[contains(@src, 'blob:') or contains(@src, 'data:image')]")
                        if len(uploaded_images) >= len(valid_paths):
                            print(f"[SUCCESS] Upload thành công tất cả {len(uploaded_images)} ảnh!")
                            upload_success = True
                            break
                        elif len(uploaded_images) > 1:
                            print(f"[PARTIAL] Upload được {len(uploaded_images)}/{len(valid_paths)} ảnh")
                            upload_success = True
                            break
                    except:
                        continue
                        
                except Exception as sep_error:
                    print(f"[WARNING] Separator '{separator}' failed: {sep_error}")
                    continue
            
            if upload_success:
                print(f"[SUCCESS] Upload multiple images thành công!")
                return True
                
        except Exception as multi_error:
            print(f"[WARNING] Multiple upload failed: {multi_error}")
        
        # Method 2: Upload từng file một nhưng vẫn trong cùng dialog
        print(f"[INFO] Thử phương pháp 2: Upload từng file trong cùng dialog...")
        try:
            # Clear input trước
            file_input.clear()
            
            # Upload từng file
            for i, path in enumerate(valid_paths):
                print(f"[INFO] Upload file {i+1}/{len(valid_paths)}: {os.path.basename(path)}")
                file_input.send_keys(path)
                time.sleep(1)  # Chờ ngắn giữa các file
            
            # Chờ tất cả ảnh được process
            wait_time = min(30, 5 + len(valid_paths) * 2)
            print(f"[INFO] Chờ {wait_time}s để xử lý {len(valid_paths)} ảnh...")
            time.sleep(wait_time)
            
            # Verify images are uploaded
            try:
                uploaded_images = driver.find_elements(By.XPATH, "//img[contains(@src, 'blob:') or contains(@src, 'data:image')]")
                print(f"[SUCCESS] Đã xác nhận {len(uploaded_images)} ảnh trong preview")
                
                if len(uploaded_images) >= len(valid_paths):
                    print(f"[SUCCESS] Upload thành công tất cả {len(valid_paths)} ảnh")
                    return True
                elif len(uploaded_images) >= len(valid_paths) * 0.5:  # Ít nhất 50%
                    print(f"[PARTIAL SUCCESS] Upload được {len(uploaded_images)}/{len(valid_paths)} ảnh")
                    return True
                else:
                    print(f"[WARNING] Chỉ upload được {len(uploaded_images)}/{len(valid_paths)} ảnh")
                    return len(uploaded_images) > 0
                    
            except Exception as preview_error:
                print(f"[WARNING] Không thể xác nhận preview: {preview_error}")
                return True  # Giả định thành công
                
        except Exception as sequential_error:
            print(f"[ERROR] Sequential upload failed: {sequential_error}")
            
        return False
            
    except Exception as e:
        print(f"[ERROR] Upload thất bại: {e}")
        traceback.print_exc()
        return False

def upload_images_backup(driver, image_paths):
    """Phương pháp backup: Upload từng ảnh một cách tuần tự với multiple file inputs"""
    try:
        print(f"[INFO] Backup method: Upload từng ảnh trong {len(image_paths)} ảnh...")
        
        success_count = 0
        
        # Method 1: Thử tìm cách add multiple files vào cùng một input
        try:
            # Tìm file input hiện tại
            file_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.XPATH, "//input[@type='file']"))
            )
            
            # Đảm bảo có thuộc tính multiple
            driver.execute_script("arguments[0].setAttribute('multiple', 'multiple');", file_input)
            
            # Thử upload theo batch nhỏ (2-3 ảnh mỗi lần)
            batch_size = 3
            for i in range(0, len(image_paths), batch_size):
                batch = image_paths[i:i+batch_size]
                print(f"[INFO] Upload batch {i//batch_size + 1}: {len(batch)} ảnh")
                
                try:
                    # Upload batch hiện tại
                    batch_paths = "\n".join(batch)
                    file_input.send_keys(batch_paths)
                    success_count += len(batch)
                    time.sleep(3)
                    
                    # Kiểm tra nếu cần thêm ảnh
                    if i + batch_size < len(image_paths):
                        # Tìm nút "Add more photos" hoặc tương tự
                        try:
                            add_more_buttons = [
                                "//div[contains(@aria-label, 'Add') and contains(@aria-label, 'photo')]",
                                "//div[contains(@aria-label, 'Thêm') and contains(@aria-label, 'ảnh')]",
                                "//div[contains(text(), 'Add Photos')]",
                                "//div[contains(text(), 'Thêm ảnh')]",
                                "//button[contains(@aria-label, 'Add')]",
                                "//input[@type='file'][not(@style*='display: none')]"
                            ]
                            
                            add_more_found = False
                            for xpath in add_more_buttons:
                                try:
                                    add_more_btn = driver.find_element(By.XPATH, xpath)
                                    if add_more_btn.is_displayed():
                                        driver.execute_script("arguments[0].click();", add_more_btn)
                                        time.sleep(2)
                                        add_more_found = True
                                        break
                                except:
                                    continue
                            
                            if add_more_found:
                                # Tìm file input mới
                                file_input = WebDriverWait(driver, 5).until(
                                    EC.presence_of_element_located((By.XPATH, "//input[@type='file']"))
                                )
                                driver.execute_script("arguments[0].setAttribute('multiple', 'multiple');", file_input)
                            
                        except Exception as add_error:
                            print(f"[WARNING] Không tìm thấy nút thêm ảnh: {add_error}")
                            break
                            
                except Exception as batch_error:
                    print(f"[ERROR] Lỗi upload batch {i//batch_size + 1}: {batch_error}")
                    continue
            
            if success_count > 0:
                print(f"[SUCCESS] Backup method: Upload {success_count}/{len(image_paths)} ảnh bằng batch method")
                return True
                
        except Exception as batch_method_error:
            print(f"[WARNING] Batch method failed: {batch_method_error}")
        
        # Method 2: Upload hoàn toàn từng file một
        print("[INFO] Thử method 2: Upload hoàn toàn từng file...")
        success_count = 0
        
        for i, image_path in enumerate(image_paths, 1):
            try:
                print(f"[INFO] Upload ảnh {i}/{len(image_paths)}: {os.path.basename(image_path)}")
                
                # Tìm file input có sẵn hoặc tạo mới
                try:
                    file_input = driver.find_element(By.XPATH, "//input[@type='file'][not(@style*='display: none')]")
                except:
                    # Nếu không tìm thấy, click add photo button
                    try:
                        add_btn = driver.find_element(By.XPATH, 
                            "//div[@aria-label='Ảnh/video' or @aria-label='Photo/video' or contains(@aria-label, 'Photo')]")
                        driver.execute_script("arguments[0].click();", add_btn)
                        time.sleep(2)
                        file_input = WebDriverWait(driver, 5).until(
                            EC.presence_of_element_located((By.XPATH, "//input[@type='file']"))
                        )
                    except Exception as btn_error:
                        print(f"[ERROR] Không thể tìm file input cho ảnh {i}: {btn_error}")
                        continue
                
                # Upload ảnh hiện tại
                file_input.send_keys(image_path)
                success_count += 1
                time.sleep(2)
                
                print(f"[SUCCESS] Upload thành công ảnh {i}: {os.path.basename(image_path)}")
                
                # Nếu không phải ảnh cuối, tìm cách thêm ảnh tiếp theo
                if i < len(image_paths):
                    # Chờ ảnh được process và tìm nút add more
                    time.sleep(3)
                
            except Exception as img_error:
                print(f"[ERROR] Lỗi upload ảnh {i}: {img_error}")
                continue
        
        # Verify final result
        if success_count > 0:
            print(f"[INFO] Chờ xử lý {success_count} ảnh...")
            time.sleep(5 + success_count * 2)
            
            try:
                images = driver.find_elements(By.XPATH, "//img[contains(@src, 'blob:') or contains(@src, 'data:image')]")
                print(f"[SUCCESS] Backup method: Upload {success_count}/{len(image_paths)} ảnh, tìm thấy {len(images)} preview")
                return success_count > 0
            except:
                print(f"[SUCCESS] Backup method: Upload {success_count}/{len(image_paths)} ảnh")
                return success_count > 0        else:
            print("[ERROR] Backup method: Không upload được ảnh nào")
            return False
            
    except Exception as e:
        print(f"[ERROR] Backup upload method failed: {e}")
        traceback.print_exc()
        return False

def upload_images_javascript_method(driver, image_paths):
    """Phương pháp thứ 3: Sử dụng JavaScript để upload multiple files"""
    try:
        print(f"[INFO] JavaScript method: Upload {len(image_paths)} ảnh bằng JavaScript...")
        
        # Kiểm tra file paths hợp lệ
        valid_paths = [path for path in image_paths if os.path.exists(path) and os.path.getsize(path) > 0]
        if not valid_paths:
            return False
        
        # Tìm file input
        file_input = WebDriverWait(driver, 15).until(
            EC.presence_of_element_located((By.XPATH, "//input[@type='file']"))
        )
        
        # Sử dụng JavaScript để tạo FileList với multiple files
        js_script = """
        var input = arguments[0];
        var paths = arguments[1];
        
        // Đảm bảo input có thuộc tính multiple
        input.setAttribute('multiple', 'multiple');
        
        // Tạo DataTransfer object để simulate file drag and drop
        var dt = new DataTransfer();
        
        // Thêm từng file vào DataTransfer (giả lập)
        for (var i = 0; i < paths.length; i++) {
            var fileName = paths[i].split('\\\\').pop();
            var file = new File([""], fileName, {type: "image/jpeg"});
            dt.items.add(file);
        }
        
        // Set files property
        input.files = dt.files;
        
        // Trigger change event
        var event = new Event('change', { bubbles: true });
        input.dispatchEvent(event);
        
        return dt.files.length;
        """
        
        result = driver.execute_script(js_script, file_input, valid_paths)
        print(f"[INFO] JavaScript method: Đã set {result} files")
        
        if result > 0:
            # Chờ JavaScript xử lý
            time.sleep(5)
            
            # Verify bằng cách check preview images
            try:
                images = driver.find_elements(By.XPATH, "//img[contains(@src, 'blob:') or contains(@src, 'data:image')]")
                if len(images) >= len(valid_paths) * 0.8:
                    print(f"[SUCCESS] JavaScript method: Upload thành công {len(images)} ảnh")
                    return True
                else:
                    print(f"[PARTIAL] JavaScript method: Upload {len(images)}/{len(valid_paths)} ảnh")
                    return len(images) > 0
            except:
                print("[INFO] JavaScript method: Không thể verify preview, giả định thành công")
                return True
        else:
            return False
            
    except Exception as e:
        print(f"[ERROR] JavaScript method failed: {e}")
        return False

def upload_images_progressive(driver, image_paths):
    """Phương pháp thứ 4: Upload dần dần từng ảnh và accumulate"""
    try:
        print(f"[INFO] Progressive method: Upload từng ảnh một cách tích lũy...")
        
        uploaded_count = 0
        for i, image_path in enumerate(image_paths, 1):
            try:
                print(f"[INFO] Progressive upload ảnh {i}/{len(image_paths)}: {os.path.basename(image_path)}")
                
                # Nếu là ảnh đầu tiên, click add photo button
                if i == 1:
                    try:
                        add_btn = WebDriverWait(driver, 10).until(
                            EC.element_to_be_clickable((
                                By.XPATH,
                                "//div[@aria-label='Ảnh/video' or @aria-label='Photo/video' or contains(@aria-label, 'Photo')]"
                            ))
                        )
                        driver.execute_script("arguments[0].click();", add_btn)
                        time.sleep(3)
                    except Exception as btn_error:
                        print(f"[ERROR] Không thể click add photo button: {btn_error}")
                        return False
                
                # Tìm file input mới nhất
                file_inputs = driver.find_elements(By.XPATH, "//input[@type='file']")
                if not file_inputs:
                    print(f"[ERROR] Không tìm thấy file input cho ảnh {i}")
                    continue
                
                # Sử dụng file input cuối cùng (mới nhất)
                file_input = file_inputs[-1]
                
                # Upload file
                file_input.send_keys(image_path)
                uploaded_count += 1
                
                # Chờ ảnh được xử lý
                time.sleep(2)
                
                # Nếu không phải ảnh cuối cùng, thử tìm cách add thêm ảnh
                if i < len(image_paths):
                    try:
                        # Chờ và tìm nút add more hoặc + button
                        time.sleep(2)
                        
                        add_more_selectors = [
                            "//div[contains(@aria-label, 'Add') and (contains(@aria-label, 'photo') or contains(@aria-label, 'image'))]",
                            "//button[contains(@aria-label, 'Add')]",
                            "//div[contains(text(), '+')]",
                            "//div[@role='button'][contains(., '+')]",
                            "//span[contains(text(), 'Add') or contains(text(), 'Thêm')]//ancestor::div[@role='button']"
                        ]
                        
                        add_more_clicked = False
                        for selector in add_more_selectors:
                            try:
                                add_more_elements = driver.find_elements(By.XPATH, selector)
                                for element in add_more_elements:
                                    if element.is_displayed() and element.is_enabled():
                                        driver.execute_script("arguments[0].click();", element)
                                        time.sleep(2)
                                        add_more_clicked = True
                                        print(f"[INFO] Clicked add more button for next image")
                                        break
                                if add_more_clicked:
                                    break
                            except:
                                continue
                        
                        if not add_more_clicked:
                            print(f"[WARNING] Không tìm thấy nút add more cho ảnh tiếp theo")
                            # Thử click vào khu vực upload để thêm ảnh
                            try:
                                upload_area = driver.find_element(By.XPATH, "//div[contains(@class, 'upload') or contains(@aria-label, 'upload')]")
                                driver.execute_script("arguments[0].click();", upload_area)
                                time.sleep(2)
                            except:
                                pass
                        
                    except Exception as add_more_error:
                        print(f"[WARNING] Lỗi khi tìm nút add more: {add_more_error}")
                
                print(f"[SUCCESS] Progressive upload ảnh {i} thành công")
                
            except Exception as img_error:
                print(f"[ERROR] Lỗi progressive upload ảnh {i}: {img_error}")
                continue
        
        # Verify kết quả cuối
        if uploaded_count > 0:
            print(f"[INFO] Progressive method: Đã upload {uploaded_count}/{len(image_paths)} ảnh")
            time.sleep(5)
            
            try:
                images = driver.find_elements(By.XPATH, "//img[contains(@src, 'blob:') or contains(@src, 'data:image')]")
                print(f"[SUCCESS] Progressive method: Tìm thấy {len(images)} ảnh trong preview")
                return len(images) > 0
            except:
                print(f"[SUCCESS] Progressive method: Upload {uploaded_count} ảnh")
                return uploaded_count > 0
        else:
            print("[ERROR] Progressive method: Không upload được ảnh nào")
            return False
            
    except Exception as e:
        print(f"[ERROR] Progressive method failed: {e}")
        traceback.print_exc()
        return False

def close_popup(driver):
    """Đóng popup nếu có"""
    try:
        # Try "Not now" button
        later = WebDriverWait(driver, 3).until(
            EC.element_to_be_clickable((By.XPATH, "//span[text()='Lúc khác' or text()='Not now']/ancestor::div[@role='button']"))
        )
        later.click()
        print("[INFO] Đã tắt popup")
    except:
        try:
            # Try close X button
            close_btn = WebDriverWait(driver, 2).until(
                EC.element_to_be_clickable((By.XPATH, "//div[@aria-label='Đóng' or @aria-label='Close']"))
            )
            close_btn.click()
            print("[INFO] Đã tắt popup")
        except:
            pass

def post_to_facebook_group(driver, group_url, cookie_file, content, hashtag, title, image_urls):
    """Đăng bài lên Facebook group với upload ảnh đơn giản"""
    if not login_with_cookies(driver, group_url, cookie_file):
        return False

    post_msg = f"{title}\n\n{content}\n\n{hashtag}"
    close_popup(driver)

    # Click on post box
    try:
        post_box = WebDriverWait(driver, 10).until(
            EC.element_to_be_clickable((
                By.XPATH,
                "//span[contains(text(), 'Bạn đang nghĩ gì?') or contains(text(), 'Bạn viết gì đi') or contains(text(), \"What's on your mind?\")]"
            ))
        )
        driver.execute_script("arguments[0].click();", post_box)
        time.sleep(2)
    except Exception as e:
        print(f"[ERROR] Cannot find post box: {e}")
        return False    # Type post content
    try:
        active_box = driver.switch_to.active_element
        active_box.send_keys(post_msg)
        time.sleep(2)
    except Exception as e:
        print(f"[ERROR] Cannot type content: {e}")
        return False    # Upload images from local folder
    upload_success = False
    
    # Lấy tất cả ảnh từ thư mục local
    image_paths = get_local_images()
    
    if image_paths:
        print(f"[INFO] Tìm thấy {len(image_paths)} ảnh trong thư mục local để upload")
        print(f"[INFO] Danh sách ảnh: {[os.path.basename(p) for p in image_paths]}")
        
        # Thử các phương pháp upload theo thứ tự ưu tiên
        upload_methods = [
            ("Phương pháp chính (Multiple upload)", upload_images_simple),
            ("Phương pháp JavaScript", upload_images_javascript_method),
            ("Phương pháp Backup (Batch upload)", upload_images_backup),
            ("Phương pháp Progressive (Từng ảnh)", upload_images_progressive)
        ]
        
        for method_name, method_func in upload_methods:
            try:
                print(f"[INFO] Đang thử {method_name}...")
                upload_success = method_func(driver, image_paths)
                
                if upload_success:
                    print(f"[SUCCESS] {method_name} thành công!")
                    break
                else:
                    print(f"[WARNING] {method_name} không thành công, thử phương pháp tiếp theo...")
                    
            except Exception as method_error:
                print(f"[ERROR] {method_name} gặp lỗi: {method_error}")
                continue
        
        # Verify final result
        if upload_success:
            print("[INFO] Upload thành công, bắt đầu xóa ảnh...")
            delete_uploaded_images(image_paths)
        else:
            print("[WARNING] Tất cả phương pháp upload đều thất bại, giữ lại ảnh")
            # Thử kiểm tra lần cuối xem có ảnh nào trong preview không
            try:
                images = driver.find_elements(By.XPATH, "//img[contains(@src, 'blob:') or contains(@src, 'data:image')]")
                if len(images) > 0:
                    print(f"[INFO] Tìm thấy {len(images)} ảnh trong preview, coi như thành công")
                    upload_success = True
                    delete_uploaded_images(image_paths)
            except:
                pass
    else:
        print("[WARNING] Không tìm thấy ảnh nào trong thư mục local, chỉ post text")
    
    # Wait a bit for processing
    time.sleep(3)

    # Click Post button
    try:
        post_btn = WebDriverWait(driver, 15).until(
            EC.element_to_be_clickable((By.XPATH, "//span[text()='Đăng' or text()='Post']/ancestor::div[@role='button']"))
        )
        driver.execute_script("arguments[0].click();", post_btn)
        time.sleep(5)
        print("[SUCCESS] Post published!")
    except Exception as e:
        print(f"[ERROR] Cannot click post button: {e}")
        return False

    close_popup(driver)
    return True

def chunk_list(lst, n):
    """Chia danh sách thành n phần"""
    avg = math.ceil(len(lst) / n)
    return [lst[i * avg: (i + 1) * avg] for i in range(n)]

def main():
    print(f"[INFO] Running on {platform.system()} {platform.release()}")
    
    try:
        sheet_data = get_google_sheet_data()
    except Exception as e:
        print(f"[ERROR] Không thể kết nối Google Sheet: {e}")
        return
        
    if not sheet_data:
        print("[ERROR] Không có dữ liệu trên Google Sheet")
        return

    title = sheet_data[0].get("Title", "").strip()
    content = sheet_data[0].get("Content", "").strip()
    hashtag = sheet_data[0].get("Hashtag", "").strip()
    image_input = sheet_data[0].get("Link ảnh", "").strip()
    
    # Process image URLs
    image_urls = process_image_urls(image_input)
    print(f"[INFO] Found {len(image_urls)} images to upload")

    accounts = [
        { "cookie": "./facebook_cookies.json" },
    ]

    group_links = [
        "https://www.facebook.com/groups/****************"
    ]

    random.shuffle(group_links)
    group_chunks = chunk_list(group_links, len(accounts))

    for acc, groups in zip(accounts, group_chunks):
        print(f"\n[INFO] Posting with account: {acc['cookie']}")
        for group_url in groups:
            driver = None
            try:
                driver = get_driver()
                print(f"[INFO] Posting to group: {group_url}")
                success = post_to_facebook_group(driver, group_url, acc["cookie"], content, hashtag, title, image_urls)
                if success:
                    print(f"[SUCCESS] Posted successfully to {group_url}")
                else:
                    print(f"[ERROR] Failed to post to {group_url}")
            except Exception as e:
                print(f"[ERROR] Error processing group {group_url}: {e}")
                traceback.print_exc()
            finally:
                if driver:
                    try:
                        driver.quit()
                    except:
                        pass
            time.sleep(60)

if __name__ == "__main__":
    main()
