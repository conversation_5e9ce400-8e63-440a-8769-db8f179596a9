#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script để kiểm tra chức năng xử lý ảnh
"""

import os
import sys
sys.stdout.reconfigure(encoding='utf-8')

# Import functions từ image_upload_handler
from image_upload_handler import get_local_images, delete_uploaded_images

def test_get_local_images():
    """Test hàm đọc ảnh từ thư mục local"""
    print("=" * 60)
    print("TEST: get_local_images()")
    print("=" * 60)
    
    image_paths = get_local_images()
    
    if image_paths:
        print(f"\n[SUCCESS] Tìm thấy {len(image_paths)} ảnh:")
        for i, path in enumerate(image_paths, 1):
            if os.path.exists(path):
                size_mb = os.path.getsize(path) / (1024 * 1024)
                print(f"  {i}. {os.path.basename(path)} - {size_mb:.2f} MB - OK")
            else:
                print(f"  {i}. {os.path.basename(path)} - FILE NOT FOUND")
    else:
        print("[WARNING] <PERSON>hông tìm thấy ảnh nào")
    
    return image_paths

def test_file_validation():
    """Test validation file"""
    print("\n" + "=" * 60)
    print("TEST: File validation")
    print("=" * 60)
    
    image_folder = r"C:\Users\<USER>\Documents\Zalo Received Files\gift\gift\image"
    
    if os.path.exists(image_folder):
        all_files = os.listdir(image_folder)
        print(f"Tất cả file trong thư mục: {all_files}")
        
        for file in all_files:
            file_path = os.path.join(image_folder, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path)
                print(f"  - {file}: {size} bytes")
            else:
                print(f"  - {file}: NOT A FILE")
    else:
        print(f"Thư mục không tồn tại: {image_folder}")

def main():
    print("KIỂM TRA CHỨC NĂNG XỬ LÝ ẢNH")
    print("=" * 60)
    
    # Test 1: Đọc ảnh
    image_paths = test_get_local_images()
    
    # Test 2: Validation
    test_file_validation()
    
    # Test 3: Hiển thị thông tin chi tiết
    if image_paths:
        print(f"\n[INFO] Sẵn sàng upload {len(image_paths)} ảnh")
        print("[INFO] Đường dẫn đầy đủ:")
        for path in image_paths:
            print(f"  - {path}")
            
        # Hỏi có muốn test delete không
        print(f"\n[NOTE] Để test chức năng xóa, gọi: delete_uploaded_images({len(image_paths)} files)")
        print("[WARNING] Chỉ xóa sau khi upload thành công!")
    
    print("\n" + "=" * 60)
    print("HOÀN THÀNH TEST")
    print("=" * 60)

if __name__ == "__main__":
    main()
